#include "pch.h"
#include "ReferencialDesignManagerHelper.h"
#include "RefDesign.Record.h"

#include <Amber/Api/Util/Exception.h>
#include <CoreApi/Util/ObjectContainerBase.h>
#include <basilica/utility/FileSystem.h>
#include <basilica/utility/MakeIterator.h>
#include <basilica/core/ArrayList.h>
#include <basilica/core/TransactionHelper.h>
#include <CoreApi/ECAD/EcadDcxAuto.h>
#include <CoreApi/ECAD/EcadDataBaseSqliteOrmManager.h>
#include <LayoutApi/ReferencialDesign/IReferencialDesignManager.h>
#include <LayoutApi/IPlacedPartManager.h>
#include <boost/range/adaptors.hpp>
#include <boost/range/algorithm/copy.hpp>
#include <CoreApi/Util/PartPathValidator.h>
#include <Amber/Api/Handler/Messenger.h>
#include <CoreApi/ComponentParser.h>
#include <CoreApi/PartLibrary.h>
#include <boost/filesystem.hpp>

#include <fstream>
#include <cctype>
#include <regex>
#include <sstream>
#include <iomanip>

#define PLANNED_QUANTITY_MIN 1
#define PLANNED_QUANTITY_MAX 9999
#define CSV_FIELD_LENGTH_MAX 80

using namespace Layout::Api::ReferencialRecord ;
using namespace Core::ECAD ;
using namespace Core::Util ;
using namespace Core::Api;

namespace Layout {
namespace Api {
namespace {

LPCTSTR DIALOG_NAME = _T( "Layout::ConfirmUpdateDialog" );
LPCTSTR PARAM_MSG_PART_ACTION_CONFIRM_UPDATE = _T( "MsgRefDesignPartsActionConfirmUpdate" );
LPCTSTR PARAM_MSG_PART_ACTION_CONFIRM_DELETE = _T( "MsgRefDesignPartsActionConfirmDelete" );
LPCTSTR PARAM_MSG_PART_ACTION_CONFIRM_UPDATE_DELETE = _T( "MsgRefDesignPartsActionConfirmUpdateDelete" );
LPCTSTR PARAM_MSG_PART_ACTION_CONFIRM_UNDONE = _T( "MsgRefDesignPartsConfirmUndone" );


///////////////////////////////////////////////////////////////////////////////////////////////////
enum class ReferencialDesignInputFileType
{
  Unknown,
  CSV,
  DCX
};

// モード関係なく図面IDは名称と比べて、あった場合、既存の図面IDを使用
void GenerateDrawingId( const std::unordered_map<PrimaryKey, IRecord*>& targetKeyRecordMap, const core::Path& targetFilePath, uint32_t& targetDrawingId )
{
  auto toLower = []( const std::wstring& s ) {
    std::wstring result = s;
    std::transform( result.begin(), result.end(), result.begin(), ::towlower );
    return result;
  };
  auto matchCondition = [targetFilePath, toLower]( const auto& it ) -> bool
  {
    return toLower( std::wstring( it.second->GetFileName() ) ) == toLower( std::wstring( targetFilePath.GetFileName().GetWString() ) );
  };
  const auto findIt = std::find_if( targetKeyRecordMap.begin(), targetKeyRecordMap.end(), matchCondition ) ;
  if ( findIt != targetKeyRecordMap.end() ) {
    targetDrawingId = findIt->second->GetDrawingId() ;
    return ;
  }
  targetDrawingId = DataBase::EcadDataBaseSqliteOrmManager::GetInstance().GetDrawingIdIndex() ;
}

///////////////////////////////////////////////////////////////////////////////////////////////////
class ReferencialDesignManagerImpl
  : public Core::Util::ObjectContainerBase<IReferencialDesignManager, HRDRecord, IRecord, IReferencialDesignListener>
  , public core::Object<IPlacedPartListener>
{
public:
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnInitialized() override
  {
    ObjectContainerBase::OnInitialized() ;
    m_keyRecordMap.clear() ;
    m_partRecordMap.clear() ;
    DataBase::EcadDataBaseSqliteOrmManager::GetInstance().Initialized();
    m_readBomMode = ReadBomMode::Replace ;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnInitialUpdate( element::IRepository* pRepository ) override
  {
    ObjectContainerBase::OnInitialUpdate( pRepository ) ;

    if ( const auto pPartManager = cast_ptr<IPlacedPartManager>( pRepository->QueryObject( PLACEDPART_MANAGER ) ) ) {
      pPartManager->AddListener( this ) ;
    }
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void OnObjectLoaded( IRecord* pObject ) override
  {
    __super::OnObjectLoaded( pObject ) ;
    for( const auto& it : pObject->GetLinkedParts() )
    {
      if( pObject->IsPlaced( it ) )
      {
        m_partRecordMap[it] = pObject->GetHandle() ;
      }
    }
    m_keyRecordMap[pObject->GetPrimaryKey()] = pObject ;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  std::shared_ptr<ReadResult> Read( const core::Path& file, core::SmartPtr<core::ArrayList<Core::ECAD::EcadBomItemInfo>> ecadBomItems ) override
  {
    auto result = std::make_shared<ReadResult>( DCXErrorCode::Success , file.GetFileName() ) ;
    if (!utility::FileSystem::IsExist(file)) {
      result->SetErrorCode( DCXErrorCode::FileNotExist ) ;
      return result ;
    }

    std::string extension = file.GetExtension().GetString().Get();

    std::transform(extension.begin(), extension.end(), extension.begin(),
      [](unsigned char c) { return std::tolower(c); });

    if (extension == ".csv") {
      uint32_t drawingId = 0;
      GenerateDrawingId( m_keyRecordMap, file, drawingId );
      m_bomFileType = ReferencialDesignInputFileType::CSV ;
      auto csvResult = ReadCsv( file, ecadBomItems , drawingId );
      if ( csvResult->GetErrorCode() != DCXErrorCode::Success ) {
        return csvResult;
      }
    }
    else if (extension == ".dcxd") {
      uint32_t drawingId = 0;
      GenerateDrawingId( m_keyRecordMap, file, drawingId );
      m_bomFileType = ReferencialDesignInputFileType::DCX ;
      result = ReadEcadDcxDrawing( file, drawingId, ecadBomItems ) ;
      if ( result->GetErrorCode() != DCXErrorCode::Success ) {
        return result;
      }
    }
    else {
      result->SetErrorCode( DCXErrorCode::FileUnsupported );
    }

    const auto mergedEcadBomItems = MergeEcadBomItem( ecadBomItems ) ;
    ecadBomItems->Clear();
    for ( auto& it : mergedEcadBomItems->Iterator() ) {
      ecadBomItems->Add( it );
    }

    return result ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void SortEcadBomItem( std::vector<EcadBomItemInfo>& ecadBomItems )
  {
    std::sort( ecadBomItems.begin(), ecadBomItems.end(), []( const EcadBomItemInfo& a, const EcadBomItemInfo& b ) {
      return std::tie( a.m_assemblyPath, a.m_deviceName ) < std::tie( b.m_assemblyPath, b.m_deviceName ) ;
    }) ;
  }


  ///////////////////////////////////////////////////////////////////////////////////////////////////
  std::shared_ptr<ReadResult> ReadMultiFile( const core::WStringEnumeration* bomFileList ) override
  {
    m_bomFileType = ReferencialDesignInputFileType::Unknown ;
    std::vector<EcadBomItemInfo> mergedEcadBomItems ;
    for(const auto& item : bomFileList->Iterator() )
    {
      const core::Path filePath( item );
      const auto ecadBomItems = core::make_smart( amnew core::ArrayList<EcadBomItemInfo>() ) ;

      auto readResult = Read( filePath, ecadBomItems );
      if ( readResult->GetErrorCode() != DCXErrorCode::Success )
      {
        return readResult ;
      }

      for ( auto& it : ecadBomItems->Iterator() )
      {
        mergedEcadBomItems.emplace_back( std::move( it ) );
      }
    }

    m_hasUpdate = false;
    m_hasDelete = false;
    AddToPoolByBomItem( mergedEcadBomItems ) ;

    for ( const auto& pPart : PlacedPartManager()->GetAll() ) {
      LinkPlacedPart( pPart );
    }

    LPCTSTR msgId = PARAM_MSG_PART_ACTION_CONFIRM_UNDONE;
    if ( m_hasUpdate && m_hasDelete ) {
      msgId = PARAM_MSG_PART_ACTION_CONFIRM_UPDATE_DELETE;
    }
    else if ( m_hasUpdate ) {
      msgId = PARAM_MSG_PART_ACTION_CONFIRM_UPDATE;
    }
    else if ( m_hasDelete ) {
      msgId = PARAM_MSG_PART_ACTION_CONFIRM_DELETE;
    }
    Messenger::ShowOKConfirm(msgId);


    return std::make_shared<ReadResult>( DCXErrorCode::Success );
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void SetReadBomMode(const ReadBomMode mode) override
  {
    m_readBomMode = mode;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ReferencialRecord::IRecord* Find( const HPlacedPart& hTarget ) const override
  {
    if ( m_partRecordMap.find( hTarget ) == m_partRecordMap.end() ) {
      return nullptr ;
    }
    return Get( m_partRecordMap.at( hTarget ) ) ;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  ReferencialRecord::IRecord* Find( const PrimaryKey& key ) const override
  {
    if (m_keyRecordMap.find( key ) == m_keyRecordMap.end()) {
      return nullptr ;
    }
    return m_keyRecordMap.at( key ) ;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  std::vector<IRecord_var> Find(
    const IRecord_var& targetObject,
    const std::function<bool( const IRecord_var& pRightObject)>& findCondition ) const override
  {
    std::vector<IRecord_var> result ;
    for ( const auto& record : m_keyRecordMap ) { 
      if ( findCondition( record.second ) ) {  
        result.emplace_back( record.second ) ;
      }
    }
    return result ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void Load( utility::Archive& ar ) override
  {
    __super::Load( ar ) ;

    size_t n_partRecordMap = 0 ;
    ar >> n_partRecordMap ;
    for ( size_t i = 0 ; i < n_partRecordMap ; ++i ) {
      HPlacedPart hPart ;
      ar >> hPart ;
      HRDRecord hRecord ;
      ar >> hRecord ;

      m_partRecordMap[hPart] = hRecord ;
    }

    for ( const auto& pObject : m_pool ) {
      m_keyRecordMap[pObject->GetPrimaryKey()] = pObject ;
    }
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void Store( utility::Archive& ar ) const override
  {
    __super::Store( ar ) ;

    ar << m_partRecordMap.size() ;
    for ( const auto& item : m_partRecordMap ) {
      ar << item.first ;
      ar << item.second ;
    }
  }

public:
  ////////////////////////////////////////////////////////////////////////////////////
  void Update( IRecord* pObject ) override
  {
    Core::ECAD::DataBase::EcadDataBaseSqliteOrmManager::GetInstance().UpdateBomItemTableOrderQuantity(
      pObject->GetPrimaryKey().GetLogicalPath().Get(),
      pObject->GetPrimaryKey().GetPartNum().Get(),
      pObject->GetDrawingId(), pObject->GetComponentHandle(),
      pObject->GetPlannedQuantity()
    ) ;
    for ( const auto& it_part : pObject->GetLinkedParts() ) {
      if ( pObject->IsPlaced( it_part ) ) {
        m_partRecordMap[it_part] = pObject->GetHandle();
      }
    }
    // undo処理：この時点では、HPlacedPart はすでに GetLinkedParts() から削除されているため、
    // undo された部品を削除し、m_partRecordMap と GetLinkedParts() を同期させる。
    for ( auto it = m_partRecordMap.begin() ; it != m_partRecordMap.end() ; ) {
      if ( it->second == pObject->GetHandle() ) {
        bool shouldErase  = true;
        for ( const auto& it_part : pObject->GetLinkedParts() ) {
          if ( it_part == it->first ) {
            shouldErase = false;
            break;
          }
        }
        if ( shouldErase ) {
          it = m_partRecordMap.erase( it ) ;
          continue;
        }
      }
      ++it ;
    }

    NotifyListener( &IReferencialDesignListener::OnRefDesignRecordModified, pObject ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void Remove( IRecord* pObject ) override
  {
    RemoveFromPool( pObject );
  }

  ////////////////////////////////////////////////////////////////////////////////////
  std::vector<ReferencialRecord::IRecord_var> GetAllBySort() override
  {
    std::vector<IRecord_var> records;
    for ( const auto& entry : m_pool ) {
      records.push_back( entry ) ;
    }

    // 　＜変更後＞以下の項目の優先順で、各項目が昇順になるようにソートする。
    //              アセンブリパス、機器番号、メーカー、形式、図面ID、構成部品ハンドル
    std::sort( records.begin(), records.end(), []( const IRecord_var ra, const IRecord_var rb ) {
      // 1. アセンブリパス（ワイド文字列比較）
      std::wstring sa = ra->GetPrimaryKey().GetLogicalPath().Get();
      std::wstring sb = rb->GetPrimaryKey().GetLogicalPath().Get();
      if ( sa != sb ) return sa < sb;
      // 2. 機器番号（ワイド文字列比較）
      sa = ra->GetPrimaryKey().GetPartNum().Get();
      sb = rb->GetPrimaryKey().GetPartNum().Get();
      if ( sa != sb ) return sa < sb;
      // 3. メーカー（ワイド文字列比較）
      sa = ra->GetMaker().Get();
      sb = rb->GetMaker().Get();
      if ( sa != sb ) return sa < sb;
      // 4. 形式（ワイド文字列比較）
      sa = ra->GetModelNum().Get();
      sb = rb->GetModelNum().Get();
      if ( sa != sb ) return sa < sb;
      // 5. 図面ID（数値比較）
      const auto da = ra->GetPrimaryKey().GetDrawingId();
      const auto db = rb->GetPrimaryKey().GetDrawingId();
      if ( da != db ) return da < db;
      // 6. 構成部品ハンドル（数値比較）
      const auto ca = ra->GetPrimaryKey().GetComponentHandle();
      const auto cb = rb->GetPrimaryKey().GetComponentHandle();
      if ( ca != cb ) return ca < cb;
      return false;
    } );
    return records;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  ReferencialRecord::IRecord* LinkPlacedPart(
    IPlacedPart* pPart
  ) override
  {
    CHECK_VALID( pPart );

    const auto primaryKey = ReferencialRecord::PrimaryKey(
      pPart->GetAssemblyPath(),
      pPart->GetDeviceNumber(),
      pPart->GetDrawingId(),
      pPart->GetBOMComponetHandle()
    );

    ReferencialRecord::IRecord* pRecord = ReferencialDesignManager()->Find( primaryKey );

    if ( !pRecord ) {
      const auto recordList = GetAllBySort();
      auto common_condition = [&]( const IRecord_var& record ) {
        return record->GetPrimaryKey().GetLogicalPath() == primaryKey.GetLogicalPath() &&
          record->GetPrimaryKey().GetPartNum() == primaryKey.GetPartNum() &&
          record->GetModelNum() == pPart->GetTypeNum();
      };

      auto it = std::find_if( recordList.begin(), recordList.end(), [&]( const auto& record ) {
        return common_condition( record ) && ( record->GetPlannedQuantity() - record->GetPlacedQuantity() > 0 );
      } );

      if ( it == recordList.end() ) {
        it = std::find_if( recordList.begin(), recordList.end(), [&]( const auto& record ) {
          return common_condition( record ) && ( record->GetPlannedQuantity() - record->GetPlacedQuantity() <= 0 );
        } );
      }

      if ( it != recordList.end() ) {
        pRecord = *it;
      }
    }

    if ( pRecord ) {
      if ( !pRecord->Has3DSymbolFile() ) {
        pRecord->SetFilePath( pPart->GetPartModelName() );
        pRecord->SetWacomPartNumber( pPart->GetComponent()->GetWacomPartNumber() );
      }
      else {
        // 配置済み3Dシンボルが更新され、更新後3Dシンボル名が指定されている
        m_hasUpdate |= Compare3DSymbolFileUpdateTime( pPart, pRecord );
      }
      const auto handle = pPart->GetHandle() ;
      if ( !pRecord->IsPlaced( handle ) ) {
        pRecord->SetPlaced( handle ) ;
      }
    }
    return pRecord ;
  }

public:
  ////////////////////////////////////////////////////////////////////////////////////
  void AddToPoolImpl( IRecord* pObject ) override
  {
    m_pool.Cache( pObject->GetHandle(), pObject ) ;
    m_keyRecordMap[pObject->GetPrimaryKey()] = pObject ;
    for ( const auto& it_part : pObject->GetLinkedParts() )
    {
      if (pObject->IsPlaced(it_part))
      {
        m_partRecordMap[it_part] = pObject->GetHandle();
      }
    }

    NotifyListener( &IReferencialDesignListener::OnRefDesignRecordAdded, pObject ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void RemoveFromPoolImpl( IRecord* pObject ) override
  {
    m_keyRecordMap.erase( pObject->GetPrimaryKey() ) ;
    RemoveEntriesByKey( m_partRecordMap , FindKeysWithValue( m_partRecordMap, pObject->GetHandle() ) );
    m_pool.Remove( pObject->GetHandle() ) ;

    NotifyListener( &IReferencialDesignListener::OnRefDesignRecordRemoved, pObject ) ;
  }

#pragma region IPlacedPartListener

public:
  ////////////////////////////////////////////////////////////////////////////////////
  void OnPlacedPartCreated( IPlacedPart* pObject ) override
  {
    if ( !IsInTransaction() ) { return ; }

    SetOrUnsetPlaced( pObject ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void OnPlacedPartModified( IPlacedPart* pObject ) override
  {
    if ( !IsInTransaction() ) { return ; }

    SetOrUnsetPlaced( pObject ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void OnPlacedPartRemoved( IPlacedPart* pObject ) override
  {
    if ( !IsInTransaction() ) { return ; }

    UnsetPlaced( pObject->GetHandle() ) ;
  }

#pragma endregion

private:
  ////////////////////////////////////////////////////////////////////////////////////
  bool IsNumber(const std::string& str)
  {
    std::regex integerRegex("^-?\\d+$");
    return std::regex_match(str, integerRegex);
  }

  ////////////////////////////////////////////////////////////////////////////////////
  bool IsValidPlannedQuantity(const int& plannedQuantity)
  {
    return (plannedQuantity >= PLANNED_QUANTITY_MIN && plannedQuantity <= PLANNED_QUANTITY_MAX);
  }

  ////////////////////////////////////////////////////////////////////////////////////
  bool IsValidField(const std::string& field)
  {
    return std::all_of(field.begin(), field.end(), [](unsigned char c) {
      return c > 0x1F && c != 0x7F;
    });
  }

  ////////////////////////////////////////////////////////////////////////////////////
  std::vector<std::string> ParseRecord(const std::string& line, int& failedFileColumn)
  {
    failedFileColumn = 1;
    std::vector<std::string> record;
    std::string parsedField;
    bool inQuotes = false;
    for (size_t i = 0; i < line.size(); ++i) {
      const char currentChar = line[i];

      if (currentChar == '"') {
        if (inQuotes) {
          if ((i + 1) < line.size()) {
            if (line[i + 1] == '"') {
              parsedField += '"';
              ++i;
            }
            else if (line[i + 1] == ',')
            {
              // 引用符が閉じられたら、inQuotesをfalseにする
              inQuotes = false;
            }
            else {
              return {};
            }
          }
          else {
            // 引用符が閉じられたら、inQuotesをfalseにする
            inQuotes = false;
          }
        }
        else {
          if (parsedField.empty()) {
            inQuotes = true;
          }
          else {
            return {};
          }
        }
      }
      else if (currentChar == ',' && !inQuotes) {
        // 引用符の外でカンマが現れたら、一つの値の終わりとする
        if (IsValidField(parsedField)) {
          record.emplace_back(parsedField);
          parsedField.clear();
          failedFileColumn++;
        }
        else {
          return {};
        }
      }
      else {
        parsedField += currentChar;
      }
    }

    if (inQuotes) {
      return {};
    }

    record.emplace_back(parsedField);
    return record;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void SkipUTF8BOM(std::ifstream &ifs)
  {
    char bom[3] = { 0 }; // 0で初期化
    ifs.read(bom, 3);
    if (!(bom[0] == '\xEF' && bom[1] == '\xBB' && bom[2] == '\xBF')) {
      ifs.seekg(0);
    }
    ifs.clear();
  }

  ////////////////////////////////////////////////////////////////////////////////////
  bool IsValidHeader(const std::string& headerLine, int& failedFileColumn)
  {
    static const std::vector<std::wstring> expectedHeaders = {
      L"アセンブリパス",
      L"機器番号",
      L"配置予定数",
      L"部品名称",
      L"メーカー",
      L"形式",
      L"部品情報"
    };
    // カラム数を調べる
    const auto headerColumns = ParseRecord(headerLine, failedFileColumn);
    if (headerColumns.size() != expectedHeaders.size()) {
      return false;
    }

    // カラム名を調べる
    for (size_t i = 0; i < expectedHeaders.size(); ++i) {
      if ( std::wstring( core::WString::UTF8ToWideChar( headerColumns[i].c_str() ) ) != expectedHeaders[i] ) {
        failedFileColumn = static_cast<int>(i) + 1;
        return false;
      }
    }
    return true;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  int GetCharacterCount( const core::String& str )
  {
    std::wstring wstr = core::WString::UTF8ToWideChar( str ) ;
    return static_cast<int>( wstr.length() ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  std::shared_ptr<CsvReadResult> ReadCsv(const core::Path& filePath, core::SmartPtr<core::ArrayList<EcadBomItemInfo>> & ecadBomItems , const uint32_t drawingId )
  {
    std::ifstream ifs(filePath.GetWString());
    if (!ifs) {
      return std::make_shared<CsvReadResult>( DCXErrorCode::FileNotFound , filePath.GetFileName(), 0 , 0) ;
    }
    int lineNumber = 0;
    int failedFileColumn = 0;

    SkipUTF8BOM(ifs);

    std::string headerStr;
    if (!std::getline(ifs, headerStr)) {
      return std::make_shared<CsvReadResult>(DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, 0);
    }

    lineNumber++;
    if (!IsValidHeader(headerStr, failedFileColumn)) {
      return std::make_shared<CsvReadResult>(DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, failedFileColumn);
    }

    std::string lineStr;
    while (std::getline(ifs, lineStr)) {
      lineNumber++;

      if (!ifs.good()) { break; }

      std::vector<std::string> record_str = ParseRecord(lineStr, failedFileColumn);
      if (record_str.size() != NUMBER_OF_COLUMNS) { return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName() , lineNumber, failedFileColumn) ; }

      core::String assemblyPath = record_str[COL_ASSEMBLY_PATH].c_str() ;
      core::String deviceName   = record_str[COL_DEVICE_NAME].c_str() ;
      core::String partName     = record_str[COL_PART_NAME].c_str() ;
      core::String maker        = record_str[COL_MAKER].c_str() ;
      core::String model        = record_str[COL_MODEL_NUMBER].c_str() ;

      if ( !PartPathValidator::ValidateAssemblyPath( core::WString::UTF8ToWideChar( assemblyPath ), false ) )
      {
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_ASSEMBLY_PATH + 1 ) ;
      }

      if ( !PartPathValidator::ValidateDeviceNumber( core::WString::UTF8ToWideChar( deviceName ), true ) )
      {
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_DEVICE_NAME + 1 ) ;
      }

      std::string strPlannedQuantity = record_str[COL_PLANNED_QUANTITY].c_str() ;
      if ( !IsNumber( strPlannedQuantity ) || !IsValidPlannedQuantity( std::stoi( strPlannedQuantity ) ) ) {
        // TODO: エラー時にエラーコードを返す処理を追加（Feature 3777で対応）。
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_PLANNED_QUANTITY + 1 ) ;
      }

      if ( GetCharacterCount( partName ) > CSV_FIELD_LENGTH_MAX )
      {
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_PART_NAME + 1 ) ;
      }

      if ( GetCharacterCount( maker ) > CSV_FIELD_LENGTH_MAX )
      {
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_MAKER + 1 ) ;
      }

      if ( GetCharacterCount( model ) > CSV_FIELD_LENGTH_MAX )
      {
        return std::make_shared<CsvReadResult>( DCXErrorCode::FormatError, filePath.GetFileName(), lineNumber, COL_MODEL_NUMBER + 1 ) ;
      }

      EcadBomItemInfo bomItem ;
      bomItem.m_assemblyPath = assemblyPath ;
      bomItem.m_deviceName = deviceName ;
      bomItem.m_orderQuantity = std::stoi(strPlannedQuantity);
      bomItem.m_partName = core::WString::UTF8ToWideChar( partName ) ;
      bomItem.m_maker = core::WString::UTF8ToWideChar( maker ) ;
      bomItem.m_typeNum = core::WString::UTF8ToWideChar( model ) ;
      bomItem.m_partInfoSummary = core::WString::UTF8ToWideChar( record_str[COL_PART_INFO].c_str() ) ;
      bomItem.m_fileName = filePath.GetFileName() ;
      bomItem.m_drawingId = drawingId ;
      bomItem.m_componentHandle = DataBase::GenerateCsvHandle( DataBase::EcadDataBaseSqliteOrmManager::GetInstance().GetCsvComponentHandleIndex() );

      ecadBomItems->Add( bomItem ) ;

      DataBase::EcadDataBaseSqliteOrmManager::GetInstance().InsertDataBase( bomItem );
    }

    return std::make_shared<CsvReadResult>( DCXErrorCode::Success );
  }

  void AddToPoolByBomItem( const std::vector<EcadBomItemInfo>& resultEcadBomItems )
  {
    auto GetReplaceRecorder = []( const std::unordered_map<PrimaryKey, IRecord*>& targetKeyRecordMap, const PrimaryKey& key ) -> IRecord*
    {
      auto matchCondition = [key]( const auto& it ) -> bool
      {
        return it.second->GetPrimaryKey() == key ;
      } ;
      const auto findIt = std::find_if( targetKeyRecordMap.begin(), targetKeyRecordMap.end(), matchCondition ) ;
      if ( findIt != targetKeyRecordMap.end() ) {
        return findIt->second ;
      }
      return nullptr ;
    };

    std::vector<PrimaryKey> recordMapKeys ;
    auto beforeKeyRecordMap = m_keyRecordMap ;
    for ( const EcadBomItemInfo& bomItem : resultEcadBomItems )
    {
      if (!IsValidPlannedQuantity(bomItem.m_orderQuantity)) { continue; }

      const auto handle = GetNextHandle();
      const auto key = PrimaryKey(
        core::WString::UTF8ToWideChar( bomItem.m_assemblyPath ),
        core::WString::UTF8ToWideChar( bomItem.m_deviceName ),
        bomItem.m_drawingId,
        bomItem.m_componentHandle
      );

      IRecord_var replaceRecorder = GetReplaceRecorder( m_keyRecordMap, key ) ;
      if ( ReadBomMode::Replace == m_readBomMode && replaceRecorder ) {
        recordMapKeys.emplace_back( key );
      }

      const auto pRecord = replaceRecorder ? replaceRecorder : Record::New(handle, key, this);
      pRecord->SetPartName(bomItem.m_partName);
      pRecord->SetMaker(bomItem.m_maker);
      pRecord->SetModelNum(bomItem.m_typeNum);
      pRecord->SetPartInfo(bomItem.m_partInfoSummary);
      pRecord->SetComponentID(bomItem.m_componentId);
      pRecord->SetPlannedQuantity( bomItem.m_orderQuantity );
      pRecord->SetWacomPartNumber( bomItem.m_wacomPartNumber );
      pRecord->SetFilePath( core::Path( bomItem.m_3DSymbolFile) );
      pRecord->SetGroupName( bomItem.m_groupName );
      pRecord->SetSubGroupName( bomItem.m_subGroupName );
      pRecord->SetFileName( bomItem.m_fileName );
      pRecord->SetBomItemHandle( bomItem.m_bomItemHandle );
      if ( replaceRecorder ) {
        pRecord->ClearBomInfos();
      }
      for ( const auto& it : bomItem.m_bomInfos ) {
        pRecord->AddBomInfo( it );
      }
      pRecord->SetManualComponent( bomItem.m_isManualComponent ) ;

      if ( !replaceRecorder ) {
        AddToPool( pRecord ) ;
      }
    }

    auto ProcessRemove = [&]() 
    {
      auto processAddMode = [&]( const std::vector<EcadBomItemInfo>& resultEcadBomItems, std::vector<ReferencialRecord::IRecord*>& removeList )
      {
        std::unordered_map<uint32_t, std::vector<const EcadBomItemInfo*>> bomItemsByDrawingId;
        for ( const auto& bomItem : resultEcadBomItems ) {
          bomItemsByDrawingId[bomItem.m_drawingId].emplace_back( &bomItem );
        }

        for ( auto& it : m_keyRecordMap ) {
          const auto drawingId = it.first.GetDrawingId();
          if ( bomItemsByDrawingId.count( drawingId ) == 0 ) {
            continue;
          }

          const auto& bomList = bomItemsByDrawingId.at( drawingId );

          const auto key = PrimaryKey(
            it.first.GetLogicalPath(),
            it.first.GetPartNum(),
            it.first.GetDrawingId(),
            it.first.GetComponentHandle()
          );
          auto matchCondition = [key]( const auto& bomItemPtr ) -> bool
          {
            const auto target = PrimaryKey(
              core::WString::UTF8ToWideChar( bomItemPtr->m_assemblyPath ),
              core::WString::UTF8ToWideChar( bomItemPtr->m_deviceName ),
              bomItemPtr->m_drawingId,
              bomItemPtr->m_componentHandle
            );
            return target == key ;
          } ;
          const auto findIt = std::find_if( bomList.begin(), bomList.end(), matchCondition ) ;
          if ( findIt == bomList.end() && std::find( removeList.begin(), removeList.end(), it.second ) == removeList.end() ) {
            removeList.emplace_back( it.second );
          }
        }
      };
      std::vector<ReferencialRecord::IRecord*> removeList ;
      if ( ReadBomMode::Replace == m_readBomMode ) {
        boost::copy( beforeKeyRecordMap
          | boost::adaptors::filtered( [&]( const auto& pair )
        {
          return std::find( recordMapKeys.begin(), recordMapKeys.end(), pair.first ) == recordMapKeys.end() ;
        } )
          | boost::adaptors::transformed( []( const auto& pair )
        {
          return pair.second ;
        } ),
          std::back_inserter( removeList )
          ) ;
      }
      else if ( ReadBomMode::Add == m_readBomMode ) {
        processAddMode( resultEcadBomItems, removeList ) ;
      }

      // 置換／追加モード：入力ファイルにない部品グループを参照設計リストから削除
      if ( removeList.size() > 0 ) {
        m_hasDelete = true; 
      }

      for ( const auto& it : removeList ) {
        if ( m_keyRecordMap.find( it->GetPrimaryKey() ) == m_keyRecordMap.end() ) {
          continue;
        }

        const auto removeDrawingId = it->GetDrawingId() ;
        const auto removeComponentHandle = it->GetComponentHandle() ;
        const std::wstring assemblyPath( it->GetPrimaryKey().GetLogicalPath().Get() ) ;
        const std::wstring deviceNumber( it->GetPrimaryKey().GetPartNum().Get() ) ;
        ReferencialDesignManager()->Remove( it );
        DataBase::EcadDataBaseSqliteOrmManager::GetInstance().RemoveComponentByKey( removeDrawingId, removeComponentHandle );
        DataBase::EcadDataBaseSqliteOrmManager::GetInstance().RemoveBomInfoByKey( assemblyPath, deviceNumber, removeDrawingId, removeComponentHandle );
      }
    };

    ProcessRemove();
  }

  ////////////////////////////////////////////////////////////////////////////////////
  core::SmartPtr<core::ArrayList<EcadBomItemInfo>> MergeEcadBomItem( const core::ArrayList<EcadBomItemInfo>* const ecadBomItems )
  {
    std::unordered_map<std::string, EcadBomItemInfo> mergedItems ;

    for ( const auto& item : ecadBomItems->Iterator() )
    {
      std::string key = std::string( item.m_assemblyPath + "_" + item.m_deviceName ) + "_" + std::to_string( item.m_drawingId ) + "_" + std::to_string( item.m_componentHandle ) ;

      auto it = mergedItems.find( key ) ;
      if (it != mergedItems.end())
      {
        it->second.m_orderQuantity += item.m_orderQuantity ;
        it->second.m_bomInfos.insert( it->second.m_bomInfos.end(), item.m_bomInfos.begin(), item.m_bomInfos.end() ) ;
      }
      else
      {
        mergedItems.insert( { key, item } ) ;
      }
    }

    auto result = core::make_smart( amnew core::ArrayList<EcadBomItemInfo>() ) ;
    for ( const auto& pair : mergedItems )
    {
      result->Add( pair.second ) ;
    }

    return result ;
  }

  std::shared_ptr<ReadResult> ReadEcadDcxDrawing(const core::Path& filePath, const uint32_t drawingId, const core::SmartPtr<core::ArrayList<EcadBomItemInfo>>& ecadBomItems )
  {
    return std::make_shared<ReadResult>( EcadDcxAuto::GetEcadDocumentBom( filePath, drawingId, ecadBomItems ), filePath.GetFileName() ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void SetOrUnsetPlaced( const IPlacedPart* pPlacedPart )
  {
    // 部品配置済み、またはUndo/Redoにより復活した場合
    const auto key = PrimaryKey( pPlacedPart->GetAssemblyPath(), pPlacedPart->GetDeviceNumber(), pPlacedPart->GetDrawingId(), pPlacedPart->GetBOMComponetHandle() ) ;
    const auto handle = pPlacedPart->GetHandle() ;

    // 4392 筐体の扉対象外
    if ( pPlacedPart->GetModelType() == ModelType::Housing && HousingManager()->GetSideByAttribute( handle.GetHandle() ) ) {
      return ;
    }

    if ( key.IsValid() )
    {
      // 既に別の項目と配置判定済みなら、一旦OFF
      UnsetPlaced( handle ) ;

      // 配置ON
      SetPlaced( handle, key ) ;
    }
    else {
      // 配置OFF
      UnsetPlaced( handle ) ;
    }
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void SetPlaced( const HPlacedPart& handle, const PrimaryKey& key )
  {
    const auto it = m_keyRecordMap.find( key ) ;
    if ( it == m_keyRecordMap.end() ) { return ; }

    const auto pRecord = it->second ;
    if ( pRecord->IsPlaced( handle ) ) { return ; }

    pRecord->SetPlaced( handle ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void UnsetPlaced( const HPlacedPart& hPart )
  {
    const auto it = m_partRecordMap.find( hPart ) ;
    if ( it == m_partRecordMap.end() ) { return ; }

    const auto hRecord = it->second ;
    const auto pRecord = Get( hRecord ) ;
    if ( !pRecord->IsPlaced( hPart ) ) { return ; }

    pRecord->UnsetPlaced( hPart ) ;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  std::vector<HPlacedPart> FindKeysWithValue( const std::unordered_map<HPlacedPart, HRDRecord>& map, const HRDRecord& valueToFind )
  {
    std::vector<HPlacedPart> keys;
    for (auto it = map.begin(); it != map.end(); ++it)
    {
      if (it->second.GetHandle() == valueToFind.GetHandle()) {
        keys.push_back( it->first );
      }
    }
    return keys;
  }

  ////////////////////////////////////////////////////////////////////////////////////
  void RemoveEntriesByKey( std::unordered_map<HPlacedPart, HRDRecord>& map, const std::vector<HPlacedPart>& keysToRemove )
  {
    for (const auto& key : keysToRemove) {
      map.erase( key );
    }
  }

  ////////////////////////////////////////////////////////////////////////////////////
  bool Compare3DSymbolFileUpdateTime( IPlacedPart* pPart, const IRecord* pRecord )
  {
    const auto pPartFilePath = LPPARTLIBRARY->GetPartModel( pPart->GetModelName() );
    const auto pRecordFilePath = LPPARTLIBRARY->GetPartModel( pRecord->GetFilePath().GetWString() );

    // 更新後3Dシンボル名が指定あり
    if ( pRecordFilePath.IsEmpty() || !basilica::utility::FileSystem::IsExist( pRecordFilePath ) )
    {
      return false;
    }


    // 更新後3Dシンボル名と配置済み部品の「3Dシンボル名」が一致
    bool isSamePath = ( pPartFilePath == pRecordFilePath );
    if ( !isSamePath )
    {
      return true; // 一致しない -> シンボル名が変わったので更新が必要。
    }

    auto refDesignPartFileTime = boost::filesystem::last_write_time( pRecordFilePath.GetString().Get() );
    // 更新後3Dシンボル名が指すファイルの更新日時が配置済み部品の「3Dシンボルファイル更新日時」と異なる
    return ( refDesignPartFileTime != pPart->Get3DSymbolFileUpdateTime() );
  }

  ////////////////////////////////////////////////////////////////////////////////////
private:
  std::unordered_map<PrimaryKey, IRecord*> m_keyRecordMap ;
  std::unordered_map<HPlacedPart, HRDRecord> m_partRecordMap ;
  ReferencialDesignInputFileType m_bomFileType ;
  Api::ReadBomMode m_readBomMode ;
  bool m_hasUpdate = false; // 更新あり
  bool m_hasDelete = false; // 削除あり
} ;

///////////////////////////////////////////////////////////////////////////////////////////////////
class ReferencialDesignManagerHelperImpl : public core::ObjectRoot<element::IRepositoryListener>
{
public:
  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnCreated( element::IRepository* pRepository ) override
  {
    pRepository->Add( REFERENCIAL_DESIGN_MANAGER, amnew ReferencialDesignManagerImpl() ) ;
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnPostCreated( element::IRepository* pRepository ) override
  {
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnRemoved( element::IRepository* pRepository ) override
  {
  }

  ///////////////////////////////////////////////////////////////////////////////////////////////////
  void OnActivate( element::IRepository* pRepository ) override
  {
  }
} ;
}//anonymous namespace

////////////////////////////////////////////////////////////////////////////////////
element::IRepositoryListener* ReferencialDesignManagerHelper::New()
{
  return amnew ReferencialDesignManagerHelperImpl() ;
}

}//Api
}//Layout

